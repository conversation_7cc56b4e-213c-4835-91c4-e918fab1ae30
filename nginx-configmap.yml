




  default.conf: |
    server {
        listen       80;
        server_name  localhost;
        location / {
            proxy_pass         http://<votre-svc-wordpress>.<ns-où-se-situe-wordpress>.svc.cluster.local;
            proxy_set_header   Host $host;
            proxy_set_header   X-Real-IP $remote_addr;
            proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header   X-Forwarded-Proto $scheme;
        }
        access_log /var/log/nginx/access.log;
        error_log  /var/log/nginx/error.log;
    }
