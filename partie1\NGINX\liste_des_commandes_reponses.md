# Liste des commandes utilisées dans la partie 1 : NGINX
question 3 : 
- kubectl apply -f nginx-deployment.yml --namespace=front
- kuberctl apply -f nginx-service.yaml --namespace=front

question 4 : 
- kubectl get events --namespace=front
- kubectl get pods --namespace=front
- kubectl logs nginx-8464bddd97-8t4lm --namespace=front
- kubectl logs nginx-8464bddd97-lqst8 --namespace=front

question 5 : 
- kubectl port-forward -n front svc/nginx 8080:8383

Difficulté : Comprendre que svc = le service et que nginx est le nom du service même si on le sait dans le fichier nginx-service.yaml


question 6 : 
- http://localhost:8080


# Réponses aux 3 questions :

question 4 : On utilise ces commandes pour vérifier que le déploiement et le service ont bien été créés dans le namespace front :
- kubectl get events --namespace=front
- kubectl logs nginx-8464bddd97-8t4lm --namespace=front  
et
- kubectl logs nginx-8464bddd97-lqst8 --namespace=front
en gros 
- kubectl logs <nom_du_pod> --namespace=front

question 7 :Le contour en pointillés violets correspond à la vision et au périmètre d’action du développeur ou DevOps. Mais aussi aux 2 pods déployés par le deployment NGINX.

question 8 :Le contour en pointillés rouges correspond à la vision et au périmètre d’action de l’administrateur du cluster. Mais aussi au namespace "front".


